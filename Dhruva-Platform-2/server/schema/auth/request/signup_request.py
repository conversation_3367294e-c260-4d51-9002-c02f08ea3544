import re
from pydantic import BaseModel, EmailStr, validator


class SignUpRequest(BaseModel):
    name: str
    email: EmailStr
    password: str

    @validator("name")
    def validate_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError("Name must be at least 2 characters long")
        if len(v.strip()) > 100:
            raise ValueError("Name must be less than 100 characters long")
        return v.strip()

    @validator("password")
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if len(v) > 128:
            raise ValueError("Password must be less than 128 characters long")

        # Enhanced password validation for security
        if not re.search(r"[A-Za-z]", v):
            raise ValueError("Password must contain at least one letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one number")

        return v

    @validator("email")
    def validate_email(cls, v):
        # Convert to lowercase for consistency
        email = v.lower()

        # Additional email validation
        if len(email) > 254:  # RFC 5321 limit
            raise ValueError("Email address is too long")

        return email
