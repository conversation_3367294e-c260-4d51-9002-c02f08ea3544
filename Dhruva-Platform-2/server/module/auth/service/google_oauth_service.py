"""
Google OAuth2 service implementation.
Handles Google OAuth2 flows, token exchange, and user information retrieval.
"""

import os
import secrets
import base64
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import httpx
from fastapi import HTTPException, status
from pydantic import BaseModel


class GoogleOAuthConfig:
    """Google OAuth2 configuration."""
    
    @property
    def CLIENT_ID(self) -> str:
        client_id = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
        if not client_id:
            # Placeholder for development - replace with actual credentials
            return "your-google-client-id.apps.googleusercontent.com"
        return client_id
    
    @property
    def CLIENT_SECRET(self) -> str:
        client_secret = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
        if not client_secret:
            # Placeholder for development - replace with actual credentials
            return "your-google-client-secret"
        return client_secret
    
    @property
    def REDIRECT_URI(self) -> str:
        redirect_uri = os.environ.get("GOOGLE_OAUTH_REDIRECT_URI")
        if not redirect_uri:
            # Default development redirect URI
            return "http://localhost:8000/auth/oauth/google/callback"
        return redirect_uri
    
    # Google OAuth2 endpoints
    AUTHORIZATION_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    TOKEN_URL = "https://oauth2.googleapis.com/token"
    USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    # Required scopes
    SCOPES = ["openid", "email", "profile"]


class GoogleUserInfo(BaseModel):
    """Google user information from OAuth."""
    id: str
    email: str
    verified_email: bool
    name: str
    given_name: str
    family_name: str
    picture: str
    locale: Optional[str] = None


class GoogleOAuthTokens(BaseModel):
    """Google OAuth tokens."""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: int
    token_type: str
    scope: str
    id_token: Optional[str] = None


class GoogleOAuthService:
    """Google OAuth2 service implementation."""
    
    def __init__(self):
        self.config = GoogleOAuthConfig()
        
        # Validate configuration
        if (self.config.CLIENT_ID.startswith("your-") or 
            self.config.CLIENT_SECRET.startswith("your-")):
            print("⚠️  WARNING: Using placeholder Google OAuth credentials!")
            print("   Please set GOOGLE_OAUTH_CLIENT_ID and GOOGLE_OAUTH_CLIENT_SECRET")
            print("   Follow the setup guide in GOOGLE_OAUTH2_CREDENTIALS_SETUP.md")
    
    def generate_pkce_pair(self) -> Tuple[str, str]:
        """
        Generate PKCE code verifier and challenge.
        
        Returns:
            Tuple of (code_verifier, code_challenge)
        """
        # Generate code verifier (43-128 characters, URL-safe)
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # Generate code challenge using SHA256
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return code_verifier, code_challenge
    
    def generate_authorization_url(
        self, 
        state: str, 
        code_challenge: str,
        redirect_uri: Optional[str] = None
    ) -> str:
        """
        Generate Google OAuth authorization URL.
        
        Args:
            state: CSRF protection state parameter
            code_challenge: PKCE code challenge
            redirect_uri: Optional custom redirect URI
            
        Returns:
            Authorization URL for redirecting user to Google
        """
        redirect_uri = redirect_uri or self.config.REDIRECT_URI
        
        params = {
            "client_id": self.config.CLIENT_ID,
            "redirect_uri": redirect_uri,
            "scope": " ".join(self.config.SCOPES),
            "response_type": "code",
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "access_type": "offline",  # Request refresh token
            "prompt": "consent",  # Force consent screen for refresh token
            "include_granted_scopes": "true"
        }
        
        # Build query string
        query_params = []
        for key, value in params.items():
            query_params.append(f"{key}={value}")
        query_string = "&".join(query_params)
        
        return f"{self.config.AUTHORIZATION_URL}?{query_string}"
    
    async def exchange_code_for_tokens(
        self, 
        code: str, 
        code_verifier: str,
        redirect_uri: Optional[str] = None
    ) -> GoogleOAuthTokens:
        """
        Exchange authorization code for access tokens.
        
        Args:
            code: Authorization code from Google
            code_verifier: PKCE code verifier
            redirect_uri: Optional custom redirect URI
            
        Returns:
            Google OAuth tokens
            
        Raises:
            HTTPException: If token exchange fails
        """
        redirect_uri = redirect_uri or self.config.REDIRECT_URI
        
        data = {
            "client_id": self.config.CLIENT_ID,
            "client_secret": self.config.CLIENT_SECRET,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
            "code_verifier": code_verifier
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.config.TOKEN_URL,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to exchange code for tokens: {error_detail}"
                    )
                
                token_data = response.json()
                return GoogleOAuthTokens(**token_data)
                
            except httpx.TimeoutException:
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail="Timeout while exchanging code for tokens"
                )
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Network error while exchanging tokens: {str(e)}"
                )
    
    async def get_user_info(self, access_token: str) -> GoogleUserInfo:
        """
        Get user information from Google.
        
        Args:
            access_token: Google OAuth access token
            
        Returns:
            Google user information
            
        Raises:
            HTTPException: If user info retrieval fails
        """
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    self.config.USERINFO_URL,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to get user info: {error_detail}"
                    )
                
                user_data = response.json()
                return GoogleUserInfo(**user_data)
                
            except httpx.TimeoutException:
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail="Timeout while getting user info"
                )
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Network error while getting user info: {str(e)}"
                )
    
    async def refresh_access_token(self, refresh_token: str) -> GoogleOAuthTokens:
        """
        Refresh Google OAuth access token.
        
        Args:
            refresh_token: Google OAuth refresh token
            
        Returns:
            New Google OAuth tokens
            
        Raises:
            HTTPException: If token refresh fails
        """
        data = {
            "client_id": self.config.CLIENT_ID,
            "client_secret": self.config.CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.config.TOKEN_URL,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to refresh token: {error_detail}"
                    )
                
                token_data = response.json()
                return GoogleOAuthTokens(**token_data)
                
            except httpx.TimeoutException:
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail="Timeout while refreshing token"
                )
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Network error while refreshing token: {str(e)}"
                )
    
    def validate_token_response(self, token_data: dict) -> bool:
        """
        Validate token response from Google.
        
        Args:
            token_data: Token response data
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["access_token", "token_type", "expires_in"]
        return all(field in token_data for field in required_fields)
