"""
Repository for OAuth state management.
Handles temporary storage and retrieval of OAuth flow state parameters.
"""

from datetime import datetime
from typing import Optional
from bson.objectid import ObjectId
from module.auth.model.oauth_state import OAuthState
from db.repository import Repository


class OAuthStateRepository(Repository[OAuthState]):
    """Repository for OAuth state management."""
    
    def __init__(self):
        super().__init__(OAuthState, "oauth_state")
    
    def find_by_state(self, state: str) -> Optional[OAuthState]:
        """
        Find OAuth state by state parameter.
        
        Args:
            state: State parameter to search for
            
        Returns:
            OAuthState if found, None otherwise
        """
        return self.find_one({"state": state})
    
    def find_valid_state(self, state: str, provider: str) -> Optional[OAuthState]:
        """
        Find valid (non-expired) OAuth state for provider.
        
        Args:
            state: State parameter to search for
            provider: OAuth provider name
            
        Returns:
            OAuthState if found and valid, None otherwise
        """
        oauth_state = self.find_by_state(state)
        if oauth_state and oauth_state.is_valid_for_provider(provider):
            return oauth_state
        return None
    
    def delete_by_state(self, state: str) -> bool:
        """
        Delete OAuth state by state parameter.
        
        Args:
            state: State parameter to delete
            
        Returns:
            True if deleted, False if not found
        """
        result = self.collection.delete_one({"state": state})
        return result.deleted_count > 0
    
    def cleanup_expired_states(self) -> int:
        """
        Clean up expired OAuth states.
        
        Returns:
            Number of expired states deleted
        """
        result = self.collection.delete_many({
            "expires_at": {"$lt": datetime.utcnow()}
        })
        return result.deleted_count
    
    def cleanup_states_for_user(self, user_id: str) -> int:
        """
        Clean up OAuth states for a specific user.
        
        Args:
            user_id: User ID to clean up states for
            
        Returns:
            Number of states deleted
        """
        result = self.collection.delete_many({"user_id": user_id})
        return result.deleted_count
    
    def get_states_for_provider(self, provider: str) -> list[OAuthState]:
        """
        Get all OAuth states for a specific provider.
        
        Args:
            provider: OAuth provider name
            
        Returns:
            List of OAuth states for the provider
        """
        return self.find({"provider": provider})
    
    def count_active_states(self) -> int:
        """
        Count active (non-expired) OAuth states.
        
        Returns:
            Number of active OAuth states
        """
        return self.collection.count_documents({
            "expires_at": {"$gt": datetime.utcnow()}
        })
    
    def get_expired_states(self) -> list[OAuthState]:
        """
        Get all expired OAuth states.
        
        Returns:
            List of expired OAuth states
        """
        return self.find({
            "expires_at": {"$lt": datetime.utcnow()}
        })
