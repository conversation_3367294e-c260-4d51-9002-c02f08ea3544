from datetime import datetime
from typing import List, Optional
from db.MongoBaseModel import MongoBaseModel
from pydantic import BaseModel, EmailStr
from schema.auth.common import RoleType


class OAuthProvider(BaseModel):
    """OAuth provider information for a user."""
    provider: str  # "google", "github", "microsoft"
    provider_user_id: str
    email: EmailStr
    verified_email: bool
    access_token: str  # Encrypted
    refresh_token: Optional[str] = None  # Encrypted
    token_expires_at: Optional[datetime] = None
    scope: str  # OAuth scopes granted
    created_at: datetime
    last_login: datetime


class User(MongoBaseModel):
    """Enhanced User model with OAuth support."""
    name: str
    email: EmailStr
    password: Optional[str] = None  # Optional for OAuth-only users
    role: RoleType
    oauth_providers: List[OAuthProvider] = []
    created_via: str = "email"  # "email", "google", "github", "microsoft"
    email_verified: bool = False
    created_at: datetime = datetime.utcnow()
    last_login: Optional[datetime] = None

    def get_oauth_provider(self, provider: str) -> Optional[OAuthProvider]:
        """Get OAuth provider info by provider name."""
        for oauth_provider in self.oauth_providers:
            if oauth_provider.provider == provider:
                return oauth_provider
        return None

    def has_oauth_provider(self, provider: str) -> bool:
        """Check if user has linked OAuth provider."""
        return self.get_oauth_provider(provider) is not None

    def is_oauth_only(self) -> bool:
        """Check if user only uses OAuth authentication."""
        return self.password is None and len(self.oauth_providers) > 0

    def can_unlink_oauth_provider(self, provider: str) -> bool:
        """Check if OAuth provider can be safely unlinked."""
        if not self.has_oauth_provider(provider):
            return False

        # If user has password, they can unlink any OAuth provider
        if self.password is not None:
            return True

        # If user is OAuth-only, they need at least one OAuth provider
        return len(self.oauth_providers) > 1
