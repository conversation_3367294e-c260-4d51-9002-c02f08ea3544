from exception.client_error import ClientErrorResponse
from fastapi import APIRout<PERSON>, Depends, Request
from schema.auth.request import RefreshRequest, SignInRequest, SignUpRequest
from schema.auth.request.email_verification_request import (
    EmailVerificationRequest,
    ResendVerificationRequest,
    RegistrationStatusRequest,
)
from schema.auth.response import RefreshResponse, SignInResponse, SignUpResponse
from schema.auth.response.email_verification_response import (
    EmailVerificationResponse,
    ResendVerificationResponse,
    RegistrationStatusResponse,
)

from ..service.auth_service import AuthService
from ..service.email_verification_service import EmailVerificationService

router = APIRouter(
    responses={"401": {"model": ClientErrorResponse}},
)


@router.post("/signin", response_model=SignInResponse)
async def _sign_in(
    request: SignInRequest, auth_service: AuthService = Depends(AuthService)
):
    res = auth_service.validate_user(request)
    return res


@router.post("/signup", response_model=SignUpResponse, status_code=201)
async def _sign_up(
    request: SignUpRequest,
    http_request: Request,
    auth_service: AuthService = Depends(AuthService)
):
    """
    Public endpoint for user registration with email verification.
    Creates a pending registration and sends verification email.
    """
    # Extract IP address and user agent for logging
    ip_address = http_request.client.host if http_request.client else None
    user_agent = http_request.headers.get("user-agent")

    res = await auth_service.register_user(request, ip_address, user_agent)
    return res


@router.get("/verify-email", response_model=EmailVerificationResponse)
async def _verify_email(
    token: str,
    http_request: Request,
    email_verification_service: EmailVerificationService = Depends(EmailVerificationService)
):
    """
    Public endpoint for email verification.
    Verifies the email token and creates the user account.
    """
    # Extract IP address and user agent for logging
    ip_address = http_request.client.host if http_request.client else None
    user_agent = http_request.headers.get("user-agent")

    res = await email_verification_service.verify_email(token, ip_address, user_agent)
    return res


@router.post("/resend-verification", response_model=ResendVerificationResponse)
async def _resend_verification(
    request: ResendVerificationRequest,
    http_request: Request,
    email_verification_service: EmailVerificationService = Depends(EmailVerificationService)
):
    """
    Public endpoint for resending verification email.
    Generates a new verification token and sends email.
    """
    # Extract IP address and user agent for logging
    ip_address = http_request.client.host if http_request.client else None
    user_agent = http_request.headers.get("user-agent")

    res = await email_verification_service.resend_verification_email(
        request.email, ip_address, user_agent
    )
    return res


@router.get("/registration-status", response_model=RegistrationStatusResponse)
async def _get_registration_status(
    email: str,
    email_verification_service: EmailVerificationService = Depends(EmailVerificationService)
):
    """
    Public endpoint for checking registration status.
    Returns the current status of email verification.
    """
    res = email_verification_service.get_registration_status(email)
    return res


@router.post("/refresh", response_model=RefreshResponse)
async def _get_access_token(
    request: RefreshRequest, auth_service: AuthService = Depends(AuthService)
):
    token = auth_service.get_refresh_token(request)
    return RefreshResponse(token=token)
