import { apiInstance } from "./apiConfig";



function timeout(delay: number) {
  return new Promise((res) => setTimeout(res, delay));
}

const login = async (userDetails : loginFormat) => {
  try {
    const response = await apiInstance.post("/auth/signin", userDetails);
    let token = response.data.token;
    let role = response.data.role;
    let user_id = response.data.id;
    if (token) {
      localStorage.setItem("refresh_token", token);
      localStorage.setItem("user_id", user_id);
    }
    if (role) {
      localStorage.setItem("user_role", role);
    }
    await timeout(1000);
    await getNewAccessToken();
    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

const getUser = async (email: string) => {
  const res = await apiInstance.get(`/auth/user?email=${email}`);
  return res.data;
};

const updateUser = async(details : UpdateProfileCreds)=>{
  const res = await apiInstance.patch(`/auth/user/modify?name=${details.name}&password=${details.password}`);
  return res.data;
}

const getNewAccessToken = async () => {
  const refreshToken = localStorage.getItem("refresh_token");
  const response = await apiInstance.post("/auth/refresh", {
    token: refreshToken,
  });
  let token = response.data.token;
  let role = response.data.role;
  if (token) {
    localStorage.setItem("access_token", token);
  }
  if (role) {
    localStorage.setItem("user_role", role);
  }
};


// OAuth Functions
const initiateGoogleOAuth = () => {
  // Redirect to backend OAuth endpoint
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://13.203.149.17:8000';
  window.location.href = `${apiUrl}/auth/oauth/google/login`;
};

const handleOAuthCallback = async (token: string) => {
  try {
    // Store OAuth token similar to regular login
    if (token) {
      localStorage.setItem("access_token", token);

      // Decode JWT to get user info (basic decode, not verification)
      const payload = JSON.parse(atob(token.split('.')[1]));
      localStorage.setItem("user_id", payload.sub);
      localStorage.setItem("user_role", payload.role);
      localStorage.setItem("email", payload.email);

      // For OAuth users, we use the access token as both access and refresh
      // In a production setup, you might want separate handling
      localStorage.setItem("refresh_token", token);
    }

    return { success: true, user: payload };
  } catch (error) {
    console.error('OAuth callback error:', error);
    throw error;
  }
};

const linkGoogleAccount = async (code: string, state: string) => {
  try {
    const response = await apiInstance.post("/auth/oauth/google/link", {
      code,
      state
    });
    return response.data;
  } catch (error) {
    console.error('Account linking error:', error);
    throw error;
  }
};

const unlinkGoogleAccount = async () => {
  try {
    const response = await apiInstance.delete("/auth/oauth/google/unlink");
    return response.data;
  } catch (error) {
    console.error('Account unlinking error:', error);
    throw error;
  }
};

const getOAuthStatus = async () => {
  try {
    const response = await apiInstance.get("/auth/oauth/google/status");
    return response.data;
  } catch (error) {
    console.error('OAuth status error:', error);
    throw error;
  }
};

export {
  login,
  getNewAccessToken,
  getUser,
  updateUser,
  initiateGoogleOAuth,
  handleOAuthCallback,
  linkGoogleAccount,
  unlinkGoogleAccount,
  getOAuthStatus
};

