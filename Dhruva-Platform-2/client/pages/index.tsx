import {
  <PERSON>rid,
  GridItem,
  Image,
  Heading,
  Stack,
  Input,
  Button,
  useMediaQuery,
  useToast,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { login, initiateGoogleOAuth } from "../api/authAPI";
import { useMutation } from "@tanstack/react-query";

export default function Login() {
  const router = useRouter();
  const toast = useToast();
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  const mutation = useMutation(login);

  useEffect(() => {
    if (
      localStorage.getItem("refresh_token") &&
      localStorage.getItem("access_token")
    ) {
      if (localStorage.getItem("currentpage")) 
      {
        router.push(localStorage.getItem("current_page"));
      } 
      else 
      {
        router.push("/services");
      }
    }
  }, []);

  const validateCredentials = () => {
    console.log('Attempting login with:', { email: username, password: password });
    mutation.mutate(
      { email: username, password: password },
      {
        onSuccess: (data) => {
          console.log('Login successful:', data);
          localStorage.setItem("email", username);
          if (localStorage.getItem("current_page")) {
            router.push(localStorage.getItem("current_page")).then(() => window.location.reload());
          } else {
            router.push("/services").then(() => window.location.reload());
          }
        },
        onError: (error: any) => {
          console.error('Login error:', error);
          if (error?.response?.status === 401 || error?.response?.status === 422) {
            toast({
              title: "Error",
              description: "Invalid Credentials",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          } else {
            toast({
              title: "Error",
              description: error?.response?.data?.message || "Something went wrong, please try again later",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          }
        },
      }
    );
  };

  // const validateCredentials = async () => {
  //   try {
  //     await login(username, password);
  //     localStorage.setItem("email", username);
  //     if(localStorage.getItem("current_page"))
  //     {
  //       router.push(localStorage.getItem("current_page"))
  //     }
  //     else
  //     {
  //       router.push(localStorage.getItem("/services"))
  //     }
  //   } catch (error) {
  //     if(error.response)
  //     {
  //     if (error.response.status === 401 || error.response.status === 422) {
  //       toast({
  //         title: "Error",
  //         description: "Invalid Credentials",
  //         status: "error",
  //         duration: 5000,
  //         isClosable: true,
  //       });
  //     } else {
  //       toast({
  //         title: "Error",
  //         description: "Something went wrong, please try again later",
  //         status: "error",
  //         duration: 5000,
  //         isClosable: true,
  //       });
  //     }
  //   }
  //  }
  // };

  return (
    <>
      <Head>
        <title>Login into Dhruva</title>
      </Head>
      {isMobile ? (
        <Grid templateColumns="repeat(1, 1fr)">
          <GridItem className="centered-column" w="100%" h="100vh">
            <Stack spacing={5}>
              <Image src="/dhruva/a4b.svg" width={104} height={104} alt="a4b" />
              <Heading>Login into Dhruva</Heading>
              <Input
                value={username}
                type="username"
                onChange={(e) => {
                  setUsername(e.target.value);
                }}
                placeholder="Username"
                size="lg"
              />
              <Input
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                }}
                type="password"
                placeholder="Password"
                size="lg"
              />
              <Button
                onClick={() => {
                  validateCredentials();
                }}
              >
                LOGIN
              </Button>

              {/* OAuth Divider */}
              <Stack direction="row" align="center" spacing={4}>
                <hr style={{ flex: 1, border: "1px solid #e2e8f0" }} />
                <span style={{ color: "#718096", fontSize: "14px" }}>OR</span>
                <hr style={{ flex: 1, border: "1px solid #e2e8f0" }} />
              </Stack>

              {/* Google OAuth Button */}
              <Button
                onClick={() => {
                  initiateGoogleOAuth();
                }}
                variant="outline"
                leftIcon={
                  <svg width="20" height="20" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                }
                size="lg"
                width="100%"
              >
                Continue with Google
              </Button>
            </Stack>
          </GridItem>
        </Grid>
      ) : (
        <Grid templateColumns="repeat(2, 1fr)">
          <GridItem
            className="centered-column"
            w="100%"
            h="100vh"
            bg="gray.100"
          >
            <Image
              src="/dhruva/dhruvaai.svg"
              width={500}
              height={500}
              alt="dhruvabot"
            />
          </GridItem>
          <GridItem className="centered-column" w="100%" h="100vh">
            <Stack spacing={5}>
              <Image src="/dhruva/a4b.svg" width={104} height={104} alt="a4b" />
              <Heading>Login into Dhruva</Heading>
              <Input
                value={username}
                type="username"
                onChange={(e) => {
                  setUsername(e.target.value);
                }}
                placeholder="Username"
                size="lg"
              />
              <Input
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                }}
                type="password"
                placeholder="Password"
                size="lg"
              />
              <Button
                onClick={() => {
                  validateCredentials();
                }}
              >
                LOGIN
              </Button>

              {/* OAuth Divider */}
              <Stack direction="row" align="center" spacing={4}>
                <hr style={{ flex: 1, border: "1px solid #e2e8f0" }} />
                <span style={{ color: "#718096", fontSize: "14px" }}>OR</span>
                <hr style={{ flex: 1, border: "1px solid #e2e8f0" }} />
              </Stack>

              {/* Google OAuth Button */}
              <Button
                onClick={() => {
                  initiateGoogleOAuth();
                }}
                variant="outline"
                leftIcon={
                  <svg width="20" height="20" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                }
                size="lg"
                width="100%"
              >
                Continue with Google
              </Button>
            </Stack>
          </GridItem>
        </Grid>
      )}
    </>
  );
}
